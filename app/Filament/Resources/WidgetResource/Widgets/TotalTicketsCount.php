<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Ticket\app\Models\Ticket;
use App\Shared\CalculatesDailyStats;

class TotalTicketsCount extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Tickets');
    }
    
    protected function getStats(): array
    {
        $totalTickets = Ticket::select('id', 'updated_at')->get();
        $ticketsUrl = url('/') . '/' . 'admin' . '/' . 'tickets';
        $ticketsChart = $this->calculateDailyStats($totalTickets, 'updated_at');

        $totalTicketsCount = $totalTickets->count();

        return [
            Stat::make(__('Total Tickets'), $totalTicketsCount)
                ->color('warning')
                ->url($ticketsUrl)
                ->chart(array_reverse($ticketsChart))
        ];
    }
}
