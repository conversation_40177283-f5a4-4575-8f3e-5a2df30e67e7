<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Mo<PERSON>les\Ticket\app\Models\Ticket;
use Modules\Ticket\Enums\TicketStatusEnum;
use App\Shared\CalculatesDailyStats;

class TotalTicketsCount extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Open Tickets');
    }

    protected function getStats(): array
    {
        $openTickets = Ticket::select('id', 'updated_at')
            ->where('status', TicketStatusEnum::OPEN)
            ->get();
        $ticketsUrl = url('/') . '/' . 'admin' . '/' . 'tickets?tableFilters[status][value]=open';
        $ticketsChart = $this->calculateDailyStats($openTickets, 'updated_at');

        $openTicketsCount = $openTickets->count();

        return [
            Stat::make(__('Total Open Tickets'), $openTicketsCount)
                ->color('warning')
                ->url($ticketsUrl)
                ->chart(array_reverse($ticketsChart))
        ];
    }
}
