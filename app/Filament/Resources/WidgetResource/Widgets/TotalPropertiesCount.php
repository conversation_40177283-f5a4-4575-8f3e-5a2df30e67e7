<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Property\app\Models\Property;
use App\Shared\CalculatesDailyStats;

class TotalPropertiesCount extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Properties');
    }
    
    protected function getStats(): array
    {
        $totalProperties = Property::select('id', 'updated_at')->get();
        $propertiesUrl = url('/') . '/' . 'admin' . '/' . 'properties';
        $propertiesChart = $this->calculateDailyStats($totalProperties, 'updated_at');

        $totalPropertiesCount = $totalProperties->count();

        return [
            Stat::make(__('Total Properties'), $totalPropertiesCount)
                ->color('success')
                ->url($propertiesUrl)
                ->chart(array_reverse($propertiesChart))
        ];
    }
}
