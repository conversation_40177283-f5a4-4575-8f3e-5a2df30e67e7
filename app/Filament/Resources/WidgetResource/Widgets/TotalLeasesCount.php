<?php

namespace App\Filament\Resources\WidgetResource\Widgets;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Modules\Lease\app\Models\Lease;
use App\Shared\CalculatesDailyStats;

class TotalLeasesCount extends BaseWidget
{
    use HasWidgetShield, CalculatesDailyStats;

    protected int | string | array $columnSpan = '1';

    protected function getColumns(): int
    {
        return $this->columnSpan;
    }

    protected function getHeading(): ?string
    {
        return __('Total Leases');
    }

    protected function getStats(): array
    {
        $totalLeases = Lease::select('id', 'updated_at')->get();
        $leasesUrl = url('/') . '/' . 'admin' . '/' . 'leases';
        $leasesChart = $this->calculateDailyStats($totalLeases, 'updated_at');

        $totalLeasesCount = $totalLeases->count();

        return [
            Stat::make(__('Total Leases'), $totalLeasesCount)
                ->color('primary')
                ->url($leasesUrl)
                ->chart(array_reverse($leasesChart))
        ];
    }
}
