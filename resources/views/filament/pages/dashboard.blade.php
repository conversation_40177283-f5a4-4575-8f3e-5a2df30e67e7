<x-filament-panels::page>
    {{-- Custom Styles for Responsive Dashboard --}}
    <style>
        /* Ensure chart widgets have consistent heights on larger screens */
        @media (min-width: 1024px) {
            .dashboard-chart-container {
                display: grid;
                align-items: stretch;
            }

            .dashboard-chart-container .dashboard-widget-container {
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .dashboard-widget-container > div {
                flex: 1;
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .fi-wi-chart {
                flex: 1;
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .fi-section {
                flex: 1;
                display: flex;
                flex-direction: column;
            }

            .dashboard-chart-container .fi-section-content {
                flex: 1;
                display: flex;
                flex-direction: column;
            }
        }

        /* Ensure widgets don't get cropped */
        .dashboard-widget-container {
            min-height: fit-content;
            overflow: visible;
        }

        /* Mobile responsiveness */
        @media (max-width: 767px) {
            .custom_widget_page .grid {
                gap: 1rem;
            }
        }
    </style>

    {{-- Welcome Widget --}}
    <div class="mb-0">
        @livewire(\App\Filament\Resources\WidgetResource\Widgets\WelcomeWidget::class)
    </div>

    <div class="grid grid-cols-12 gap-4 lg:gap-8 custom_widget_page">

        {{-- Widgets Section --}}
        <div class="col-span-12 lg:col-span-12 grid ">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-4">

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalLeasesCount::class)

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalInvoicesCount::class)

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalPropertiesCount::class)

                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalTicketsCount::class)

            </div>
        </div>

        {{-- Custom Widgets Section --}}
        <div class="col-span-12 lg:col-span-12 grid ">
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                @can('widget_CompanyCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\CompanyCount::class)
                @endcan
                @can('widget_ActiveLeaseCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\ActiveLeaseCount::class)
                @endcan
                @can('widget_InactiveLeaseCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\InactiveLeaseCount::class)
                @endcan

                @can('widget_EjarRegisteredLeaseCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\EjarRegisteredLeaseCount::class)
                @endcan
                @can('widget_ActiveMaintenanceRequestsCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\ActiveMaintenanceRequestsCount::class)
                @endcan
                @can('widget_InactiveMaintenanceRequestsCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\InactiveMaintenanceRequestsCount::class)
                @endcan

                @can('widget_CommissionInvoicesTotal')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\CommissionInvoicesTotal::class)
                @endcan
                @can('widget_TotalPaidPayments')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalPaidPayments::class)
                @endcan
                @can('widget_TotalUnpaidPayments')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalUnpaidPayments::class)
                @endcan

                @can('widget_InactiveSubscription')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\InactiveSubscription::class)
                @endcan
                @can('widget_ActiveSubscription')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\ActiveSubscription::class)
                @endcan
                @can('widget_TotalSubscriptionIncome')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalSubscriptionIncome::class)
                @endcan

                @can('widget_TotalSubscriptionUpcome')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalSubscriptionUpcome::class)
                @endcan
                @can('widget_ProperitiesManagedCount')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\ProperitiesManagedCount::class)
                @endcan

                @can('widget_TotalRentCurrentMonth')
                    @livewire(\App\Filament\Resources\WidgetResource\Widgets\TotalRentCurrentMonth::class)
                @endcan
            </div>
        </div>

        {{-- Charts Section --}}
        <div class="col-span-12 lg:col-span-12">
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6 dashboard-chart-container">
                {{-- Lease Analytics Chart --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full min-h-[300px] lg:min-h-[350px]">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\LeaseAnalyticsWidget::class)
                    </div>
                </div>

                {{-- Invoice Analytics Chart --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full min-h-[300px] lg:min-h-[350px]">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\InvoiceAnalyticsWidget::class)
                    </div>
                </div>

                {{-- Property Analytics Chart --}}
                <div class="col-span-1 md:col-span-2 xl:col-span-1 dashboard-widget-container">
                    <div class="h-full min-h-[300px] lg:min-h-[350px]">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\PropertyAnalyticsWidget::class)
                    </div>
                </div>
            </div>
        </div>

        {{-- Table Widgets Section --}}
        <div class="col-span-12">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                {{-- Latest Leases Widget --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\LatestLeasesWidget::class)
                    </div>
                </div>

                {{-- Latest Invoices Widget --}}
                <div class="col-span-1 dashboard-widget-container">
                    <div class="h-full">
                        @livewire(\App\Filament\Resources\WidgetResource\Widgets\LatestInvoicesWidget::class)
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
